#!/usr/bin/env python3
"""
测试内嵌的高级PID控制器
验证main.py中内嵌的PID代码是否正常工作

@author: AI Assistant
@date: 2025.8.2
"""

import math

# 从main.py中复制的内嵌PID代码
def limit_step(current, last, max_step=2):
    """限制输出变化率，防止舵机运动过于剧烈"""
    delta = current - last
    if abs(delta) > max_step:
        return last + (max_step if delta > 0 else -max_step)
    else:
        return current

class AdvancedPIDController:
    """高级PID控制器（参考222.py）"""
    
    def __init__(self, Kp, Ki, Kd, error_threshold=8, integral_limit=80, min_output=3, max_output=40, max_step=2):
        self.Kp = Kp
        self.Ki = Ki
        self.Kd = Kd
        self.error_threshold = error_threshold  # 积分分离阈值
        self.integral_limit = integral_limit    # 积分限幅
        self.min_output = min_output           # 最小输出
        self.max_output = max_output           # 最大输出
        self.max_step = max_step               # 输出变化率限制
        
        # 内部状态
        self.last_error = 0
        self.integral = 0
        self.last_output = 0
        self.debug = False

    def compute(self, error):
        """计算PID输出"""
        # 调试：打印PID状态
        if self.debug and abs(error) > 1:
            print(f"🔧 PID计算前 - 误差:{error:.2f}, 积分:{self.integral:.3f}, 上次误差:{self.last_error:.3f}")

        # 微分项
        derivative = error - self.last_error
        self.last_error = error

        # 积分分离：小误差才积分，防止大误差时积分饱和
        if abs(error) > self.error_threshold:
            if self.debug:
                print(f"🚫 大误差({abs(error):.1f} > {self.error_threshold})，跳过积分")
        else:
            self.integral += error
            self.integral = max(min(self.integral, self.integral_limit), -self.integral_limit)
            if self.debug:
                print(f"✅ 小误差积分，当前积分值: {self.integral:.3f}")

        # PID计算
        p_term = self.Kp * error
        i_term = self.Ki * self.integral
        d_term = self.Kd * derivative
        output = p_term + i_term + d_term

        if self.debug:
            print(f"📊 PID项: P={p_term:.2f}, I={i_term:.2f}, D={d_term:.2f}, 总输出={output:.2f}")

        # 限制输出变化率
        output = limit_step(output, self.last_output, max_step=self.max_step)
        self.last_output = output

        # 输出限幅和死区补偿
        if output > 0:
            output = max(min(output, self.max_output), self.min_output)
        elif output < 0:
            output = min(max(output, -self.max_output), -self.min_output)
        else:
            output = 0

        if self.debug:
            print(f"🎯 最终输出: {output:.2f} (限幅后)")

        return output
    
    def reset(self):
        """重置PID状态"""
        self.last_error = 0
        self.integral = 0
        self.last_output = 0
        if self.debug:
            print("🔄 PID状态已重置")
    
    def set_debug(self, debug):
        """设置调试模式"""
        self.debug = debug

class ServoStabilityController:
    """舵机稳定性控制器（参考222.py）"""
    
    def __init__(self, stability_check_frames=5, max_stable_error=6, error_dead_zone=5):
        self.stability_check_frames = stability_check_frames
        self.max_stable_error = max_stable_error
        self.error_dead_zone = error_dead_zone
        
        # 稳定性跟踪
        self.stable_frame_count = 0
        self.last_errors = []
        self.is_stable = False
        self.debug = False
    
    def update_stability(self, err_x, err_y):
        """更新稳定性状态"""
        total_error = math.sqrt(err_x**2 + err_y**2)
        
        # 更新误差历史
        self.last_errors.append(total_error)
        if len(self.last_errors) > self.stability_check_frames:
            self.last_errors.pop(0)
        
        # 检查稳定性
        if len(self.last_errors) >= self.stability_check_frames:
            max_recent_error = max(self.last_errors)
            if max_recent_error < self.max_stable_error:
                self.stable_frame_count += 1
                self.is_stable = True
                if self.debug:
                    print(f"✅ 系统稳定: 最大误差{max_recent_error:.1f} < {self.max_stable_error}")
            else:
                self.stable_frame_count = 0
                self.is_stable = False
                if self.debug:
                    print(f"❌ 系统不稳定: 最大误差{max_recent_error:.1f} >= {self.max_stable_error}")
        
        return self.is_stable
    
    def should_move(self, err_x, err_y):
        """判断是否应该移动舵机"""
        current_dead_zone = self.error_dead_zone
        if self.is_stable:
            current_dead_zone = self.error_dead_zone * 1.5  # 稳定时死区增大50%
        
        # 死区判断
        if abs(err_x) < current_dead_zone and abs(err_y) < current_dead_zone:
            if self.debug:
                status = "稳定" if self.is_stable else "普通"
                print(f"💤 误差在死区内: X={err_x:+.1f}, Y={err_y:+.1f} (死区±{current_dead_zone:.1f}, {status})")
            return False
        
        # 如果刚刚稳定，给一个短暂的延迟
        if self.is_stable and self.stable_frame_count < self.stability_check_frames + 2:
            if self.debug:
                print(f"⏳ 稳定延迟中: {self.stable_frame_count}/{self.stability_check_frames + 2}")
            return False
        
        return True
    
    def reset_stability(self):
        """重置稳定性状态"""
        self.stable_frame_count = 0
        self.is_stable = False
        self.last_errors.clear()
        if self.debug:
            print("🔄 稳定性状态已重置")
    
    def set_debug(self, debug):
        """设置调试模式"""
        self.debug = debug

class AdvancedServoController:
    """高级舵机控制器（整合PID和稳定性控制）"""
    
    def __init__(self, pid_params=None, stability_params=None, debug=False):
        # 默认PID参数（参考222.py的优化参数）
        if pid_params is None:
            pid_params = {
                "Kp": 0.2, "Ki": 0.008, "Kd": 0.75,
                "error_threshold": 8, "integral_limit": 10,
                "min_output": 1, "max_output": 15, "max_step": 2
            }
        
        # 默认稳定性参数
        if stability_params is None:
            stability_params = {
                "stability_check_frames": 5,
                "max_stable_error": 6,
                "error_dead_zone": 5
            }
        
        # 创建PID控制器
        self.pid_x = AdvancedPIDController(**pid_params)
        self.pid_y = AdvancedPIDController(**pid_params)
        
        # 创建稳定性控制器
        self.stability = ServoStabilityController(**stability_params)
        
        # 设置调试模式
        self.debug = debug
        self.pid_x.set_debug(debug)
        self.pid_y.set_debug(debug)
        self.stability.set_debug(debug)
        
        # 偏移补偿参数
        self.offset_x = -12
        self.offset_y = -12
    
    def compute_control(self, target_x, target_y, center_x, center_y):
        """计算控制输出"""
        # 计算误差（加入偏移补偿）
        err_x = target_x - (center_x + self.offset_x)
        err_y = target_y - (center_y + self.offset_y)
        
        # 更新稳定性状态
        is_stable = self.stability.update_stability(err_x, err_y)
        
        # 判断是否应该移动
        should_move = self.stability.should_move(err_x, err_y)
        
        control_x = 0
        control_y = 0
        
        if should_move:
            # 计算PID控制输出
            control_x = self.pid_x.compute(err_x)
            control_y = self.pid_y.compute(-err_y)  # Y轴方向相反
            
            # 重置稳定计数
            self.stability.reset_stability()
            
            if self.debug:
                print(f"🎮 控制输出: X={control_x:.2f}, Y={control_y:.2f}")
        
        # 误差信息
        error_info = {
            "err_x": err_x, "err_y": err_y, "is_stable": is_stable,
            "should_move": should_move, "total_error": math.sqrt(err_x**2 + err_y**2)
        }
        
        return should_move, control_x, control_y, error_info
    
    def reset_all(self):
        """重置所有状态"""
        self.pid_x.reset()
        self.pid_y.reset()
        self.stability.reset_stability()
        if self.debug:
            print("🔄 所有控制器状态已重置")
    
    def set_debug(self, debug):
        """设置调试模式"""
        self.debug = debug
        self.pid_x.set_debug(debug)
        self.pid_y.set_debug(debug)
        self.stability.set_debug(debug)

def test_embedded_pid():
    """测试内嵌的高级PID控制器"""
    print("🧪 测试内嵌的高级PID控制器")
    print("=" * 40)
    
    # 创建控制器
    controller = AdvancedServoController(debug=True)
    
    # 测试数据
    center_x, center_y = 224, 224
    test_cases = [
        (274, 194, "目标在右上"),
        (224, 224, "目标在中心"),
        (174, 254, "目标在左下")
    ]
    
    for target_x, target_y, description in test_cases:
        print(f"\n📍 测试: {description}")
        print(f"   目标: ({target_x}, {target_y}), 中心: ({center_x}, {center_y})")
        
        should_move, control_x, control_y, error_info = controller.compute_control(
            target_x, target_y, center_x, center_y
        )
        
        print(f"   误差: X={error_info['err_x']:+.1f}, Y={error_info['err_y']:+.1f}")
        print(f"   控制: X={control_x:+.2f}, Y={control_y:+.2f}")
        print(f"   状态: {'移动' if should_move else '静止'}")
    
    print("\n✅ 内嵌PID控制器测试完成")

if __name__ == "__main__":
    test_embedded_pid()
